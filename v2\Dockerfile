# ===========================================
# 🐳 TOKENTRACKER V2 DOCKERFILE
# ===========================================
# Multi-stage build following DEPLOYMENT_SCALING.md guidelines

# 🏗️ Base stage with Python and system dependencies
FROM python:3.11-slim as base

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# Create app directory
WORKDIR /app

# 📦 Dependencies stage
FROM base as deps

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# 🧪 Development stage
FROM deps as development

# Install development dependencies
RUN pip install --no-cache-dir \
    pytest \
    pytest-asyncio \
    pytest-cov \
    black \
    flake8 \
    mypy

# Copy source code
COPY . .

# Create non-root user for development
RUN groupadd -r appuser && useradd -r -g appuser appuser
RUN chown -R appuser:appuser /app
USER appuser

# Expose port
EXPOSE 3000

# Development command
CMD ["python", "-m", "uvicorn", "src.app:app", "--host", "0.0.0.0", "--port", "3000", "--reload"]

# 🚀 Production stage
FROM deps as production

# Copy only necessary files
COPY src/ ./src/
COPY .env.example .env.example

# Create non-root user for security
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Create necessary directories
RUN mkdir -p /app/logs /app/data && \
    chown -R appuser:appuser /app

# Switch to non-root user
USER appuser

# Health check with comprehensive monitoring endpoints
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/health && \
        curl -f http://localhost:3000/ready && \
        curl -f http://localhost:3000/metrics || exit 1

# Expose port
EXPOSE 3000

# Production command
CMD ["python", "-m", "uvicorn", "src.app:app", "--host", "0.0.0.0", "--port", "3000", "--workers", "4"]

# 🧪 Testing stage
FROM development as testing

# Copy test files
COPY tests/ ./tests/

# Run tests
RUN python -m pytest tests/ -v --cov=src --cov-report=term-missing

# 📊 Monitoring stage (for metrics collection)
FROM production as monitoring

# Install monitoring tools
USER root
RUN pip install --no-cache-dir \
    prometheus-client \
    psutil

USER appuser

# Expose metrics port
EXPOSE 9090

# Command with metrics
CMD ["python", "-m", "uvicorn", "src.app:app", "--host", "0.0.0.0", "--port", "3000", "--workers", "4"]
